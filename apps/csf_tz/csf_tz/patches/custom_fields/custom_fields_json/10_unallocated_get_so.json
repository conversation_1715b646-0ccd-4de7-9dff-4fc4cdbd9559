[{"name": "Payment Entry-custom_get_outstanding_so", "owner": "Administrator", "creation": "2025-05-23 09:14:09.851930", "modified": "2025-05-23 09:15:04.386949", "modified_by": "Administrator", "docstatus": 0, "idx": 50, "is_system_generated": 0, "dt": "Payment Entry", "label": "Get Outstanding SO", "fieldname": "get_outstanding_so", "insert_after": "get_outstanding_orders", "length": 0, "fieldtype": "<PERSON><PERSON>", "precision": "", "hide_seconds": 0, "hide_days": 0, "sort_options": 0, "fetch_if_empty": 0, "collapsible": 0, "non_negative": 0, "reqd": 0, "unique": 0, "is_virtual": 0, "read_only": 0, "ignore_user_permissions": 0, "hidden": 0, "print_hide": 0, "print_hide_if_no_value": 0, "no_copy": 0, "allow_on_submit": 0, "in_list_view": 0, "in_standard_filter": 0, "in_global_search": 0, "in_preview": 0, "bold": 0, "report_hide": 0, "search_index": 0, "allow_in_quick_entry": 0, "ignore_xss_filter": 0, "translatable": 0, "hide_border": 0, "show_dashboard": 0, "permlevel": 0, "columns": 0, "doctype": "Custom Field", "__last_sync_on": "2025-05-23T09:33:07.340Z"}]