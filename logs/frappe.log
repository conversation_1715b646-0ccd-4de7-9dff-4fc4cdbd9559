2025-07-09 19:09:40,995 ERROR frappe Failed to capture exception
Site: mysite
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-10 11:17:44,638 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:24:18,556 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:24:48,624 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:24:56,746 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:25:28,840 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:28:20,850 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{"from_date":"2025-06-01","to_date":"2025-07-10"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-10 11:35:16,559 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Bank Trans vs GL Entry Report', 'filters': '{"from_date":"2025-06-01","to_date":"2025-07-10"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-07-11 15:21:54,850 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'doc': '{"docstatus":0,"doctype":"Employee","name":"new-employee-wjigafztdk","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"HR-EMP-","employee_country":"Tanzania","status":"Active","create_user_permission":1,"company":"Mysite (Demo)","pension_fund":"","employee_salary_component_limit":[],"prefered_contact_email":"","unsubscribed":0,"current_accommodation_type":"","permanent_accommodation_type":"","salary_currency":"TZS","salary_mode":"","bank_country":"Tanzania","employee_ot_component":[],"marital_status":"","blood_group":"","files":[],"education":[],"external_work_history":[],"internal_work_history":[],"leave_encashed":"","employee_country_code":"tz","bank_country_code":"tz","first_name":"Phenelist","middle_name":"Simon","last_name":"Metumba","gender":"Male","date_of_birth":"2000-02-02","date_of_retirement":"2060-02-02","date_of_joining":"2025-04-01"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-13 21:47:28,452 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'doc': '{"docstatus":0,"doctype":"Employee","name":"new-employee-zoqqrifgma","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"HR-EMP-","employee_country":"Tanzania","status":"Active","create_user_permission":1,"company":"Mysite","pension_fund":"","employee_salary_component_limit":[],"prefered_contact_email":"","unsubscribed":0,"current_accommodation_type":"","permanent_accommodation_type":"","salary_currency":"TZS","salary_mode":"","bank_country":"Tanzania","employee_ot_component":[],"marital_status":"","blood_group":"","files":[],"education":[],"external_work_history":[],"internal_work_history":[],"leave_encashed":"","employee_country_code":"tz","bank_country_code":"tz","first_name":"crispine","last_name":"Alex","gender":"Male","date_of_birth":"1980-01-01","date_of_retirement":"2040-01-01","date_of_joining":"2025-02-05","designation":"Senior Software Engineer"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-13 21:49:28,103 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'doc': '{"docstatus":0,"doctype":"Employee","name":"new-employee-cznlxwhjuc","__islocal":1,"__unsaved":1,"owner":"Administrator","naming_series":"HR-EMP-","employee_country":"Tanzania","status":"Active","create_user_permission":1,"company":"Mysite","pension_fund":"","employee_salary_component_limit":[],"prefered_contact_email":"","unsubscribed":0,"current_accommodation_type":"","permanent_accommodation_type":"","salary_currency":"TZS","salary_mode":"","bank_country":"Tanzania","employee_ot_component":[],"marital_status":"","blood_group":"","files":[],"education":[],"external_work_history":[],"internal_work_history":[],"leave_encashed":"","employee_country_code":"tz","bank_country_code":"tz","first_name":"Phenelist","middle_name":"Simon","last_name":"Metumba","designation":"Chief Executive Officer","gender":"Male","date_of_birth":"1970-05-06","date_of_retirement":"2030-05-06","date_of_joining":"2020-02-05"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-07-14 09:05:17,698 ERROR frappe Failed to capture exception
Site: mysite
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 10:14:17,350 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'cv_profile_name': 'CV-HR-EMP-00001', 'valid_days': '30', 'cmd': 'cv_maker.api.generate_public_link'}
2025-07-14 12:24:04,776 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'report_name': 'Used Items Report', 'filters': '{}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-14 16:30:03,092 ERROR frappe Failed to capture exception
Site: mysite
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 117, in capture_exception
    set_scope(scope)
  File "/home/<USER>/dev/mybench/apps/frappe/frappe/utils/sentry.py", line 70, in set_scope
    path = frappe.request.path
           ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/dev/mybench/env/lib/python3.12/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-07-14 17:12:15,941 ERROR frappe Error creating Custom Field Property-custom_territory: A field with the name territory already exists in Property
Site: mysite
Form Dict: {}
2025-07-18 10:00:35,413 ERROR frappe New Exception collected in error log
Site: mysite
Form Dict: {'doc': '{"name":"CSF TZ Settings","owner":"Administrator","modified":"2025-07-14 19:00:02.730475","modified_by":"Administrator","docstatus":0,"idx":"0","unique_records":0,"validate_net_rate":0,"fetch_default_tax_category":0,"enable_dependent_auto_permission":1,"limit_uom_as_item_uom":1,"show_customer_outstanding_in_sales_order":0,"allow_reopen_of_po_based_on_role":0,"allow_reopen_of_material_request_based_on_role":0,"disable_get_outstanding_functionality":0,"is_manufacture":0,"allow_delete_in_sql_command":0,"allow_batch_splitting":0,"item_qty_poppup_message":0,"validate_grand_total_vs_payment_amount_on_sales_invoice":1,"target_warehouse_based_price_list":0,"enable_trade_in":0,"override_sales_invoice_qty":0,"enable_payroll_approval":0,"override_salary_slip_email_message":0,"enable_fixed_working_days_per_month":0,"working_days_per_month":26,"override_email_queue_batch_size":0,"email_qatch_batch_size":0,"enable_overtime_calculation":0,"override_fetch_shift_details":0,"working_hours_per_month":0,"ot_module":0,"doctype":"CSF TZ Settings","__last_sync_on":"2025-07-18T07:00:26.936Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
