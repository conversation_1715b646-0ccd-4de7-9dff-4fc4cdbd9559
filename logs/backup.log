set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 12:00:06.700144
Config  : ./mysite/private/backups/20250710_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250710_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-10 18:00:07.339341
Config  : ./mysite/private/backups/20250710_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250710_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250711_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-11 12:00:04.318513
Config  : ./mysite/private/backups/20250711_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250711_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250711_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-11 18:00:10.872747
Config  : ./mysite/private/backups/20250711_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250711_180003-mysite-database.sql.gz         1.3MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250713_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-13 12:00:05.704913
Config  : ./mysite/private/backups/20250713_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250713_120002-mysite-database.sql.gz         1.3MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250713_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-13 18:00:08.975396
Config  : ./mysite/private/backups/20250713_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250713_180003-mysite-database.sql.gz         1.4MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250714_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-14 12:00:06.280038
Config  : ./mysite/private/backups/20250714_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250714_120002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250714_180002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-14 18:00:05.452006
Config  : ./mysite/private/backups/20250714_180002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250714_180002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250715_000002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-15 00:00:05.272616
Config  : ./mysite/private/backups/20250715_000002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250715_000002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250715_120002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-15 12:00:06.476787
Config  : ./mysite/private/backups/20250715_120002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250715_120002-mysite-database.sql.gz         1.1MiB
Backup for Site mysite has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250715_120006-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-15 12:00:10.313846
Config  : ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250715_120006-test-site-database.sql.gz         847.1KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250715_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-15 18:00:08.201253
Config  : ./mysite/private/backups/20250715_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250715_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250715_180008-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-15 18:00:11.499309
Config  : ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250715_180008-test-site-database.sql.gz         855.8KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_000003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 00:00:08.251621
Config  : ./mysite/private/backups/20250716_000003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_000003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_000008-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 00:00:11.580636
Config  : ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_000008-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_060002-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 06:00:06.045872
Config  : ./mysite/private/backups/20250716_060002-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_060002-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_060006-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 06:00:08.629075
Config  : ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_060006-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_120003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 12:00:08.821596
Config  : ./mysite/private/backups/20250716_120003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_120003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_060006-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_120009-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 12:00:12.588483
Config  : ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_120009-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250716_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-16 18:00:07.905047
Config  : ./mysite/private/backups/20250716_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250716_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_060006-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_120009-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250715_120006-test-site-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250716_180008-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-16 18:00:11.134758
Config  : ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250716_180008-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250717_120001-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-17 12:00:04.369069
Config  : ./mysite/private/backups/20250717_120001-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250717_120001-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_000008-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250715_180008-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250716_060006-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250716_120009-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250715_180008-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_060006-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250716_000008-test-site-database.sql.gz is older than 24 hours
set -o pipefail; /usr/bin/mariadb-dump --user=_7574cff83173b5ba --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _7574cff83173b5ba | /usr/bin/gzip >> ./test-site/private/backups/20250717_120004-test-site-database.sql.gz

Backup Summary for test-site at 2025-07-17 12:00:06.043074
Config  : ./test-site/private/backups/20250717_120004-test-site-site_config_backup.json 94.0B
Database: ./test-site/private/backups/20250717_120004-test-site-database.sql.gz         856.4KiB
Backup for Site test-site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250717_180003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-17 18:00:09.780595
Config  : ./mysite/private/backups/20250717_180003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250717_180003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_120009-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250716_120009-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250717_120004-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_180008-test-site-database.sql.gz is recent
File ./test-site/private/backups/20250717_120004-test-site-database.sql.gz is recent
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x704b33f29080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x704b34982570>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704b363f4f40>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704b363f4f40>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x704b346fcef0>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x704b363f4f40>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x704b346fcef0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x704b333697f0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x704b3331d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x704b3331da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x704b363f5220>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x704b33e25580>, <class 'int'>: <function escape_int at 0x704b33e25620>, <class 'float'>: <function escape_float at 0x704b33e256c0>, <class 'str'>: <function escape_str at 0x704b33e25940>, <class 'bytes'>: <function escape_bytes at 0x704b33e258a0>, <class 'tuple'>: <function escape_sequence at 0x704b33e25440>, <class 'list'>: <function escape_sequence at 0x704b33e25440>, <class 'set'>: <function escape_sequence at 0x704b33e25440>, <class 'frozenset'>: <function escape_sequence at 0x704b33e25440>, <class 'dict'>: <function escape_dict at 0x704b33e253a0>, <class 'NoneType'>: <function escape_None at 0x704b33e259e0>, <class 'datetime.date'>: <function escape_date at 0x704b33e25c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x704b33e25bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x704b33e25a80>, <class 'datetime.time'>: <function escape_time at 0x704b33e25b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x147\xb1\xee\xfb\xb3\xa1\x90\xa3\xd3\xad\xbe\xa4\xa8C \x86",\xbf\xed_7574cff83173b5ba\x00mysql_native_password\x006\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0529018'
      authresp = b'7\xb1\xee\xfb\xb3\xa1\x90\xa3\xd3\xad\xbe\xa4\xa8C \x86",\xbf\xed'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x0529018'
      k = b'_pid'
      v = b'29018'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x704b3336b740>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x704b333d8e50>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x704b333d8e50>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
set -o pipefail; /usr/bin/mariadb-dump --user=_0f0a36aa4cd25b59 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _0f0a36aa4cd25b59 | /usr/bin/gzip >> ./mysite/private/backups/20250718_000003-mysite-database.sql.gz

Backup Summary for mysite at 2025-07-18 00:00:08.803470
Config  : ./mysite/private/backups/20250718_000003-mysite-site_config_backup.json 158.0B
Database: ./mysite/private/backups/20250718_000003-mysite-database.sql.gz         1.2MiB
Backup for Site mysite has been successfully completed
File ./test-site/private/backups/20250716_180008-test-site-site_config_backup.json is older than 24 hours
File ./test-site/private/backups/20250717_120004-test-site-site_config_backup.json is recent
File ./test-site/private/backups/20250716_180008-test-site-database.sql.gz is older than 24 hours
File ./test-site/private/backups/20250717_120004-test-site-database.sql.gz is recent
Backup failed for Site test-site. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['mysite', 'test-site'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7e012a441080>
      exit_code = 0
      rollback_callback = None
      site = 'test-site'
      odb = <frappe.utils.backups.BackupGenerator object at 0x7e012a8de630>
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e012c38d180>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e012c38d180>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e012aecbc80>
      db_name = '_7574cff83173b5ba'
      user = '_7574cff83173b5ba'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e012c38d180>
      site = 'test-site'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e012aecbc80>
  File "apps/frappe/frappe/database/mariadb/database.py", line 484, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7e01292cb4a0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_7574cff83173b5ba'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7e012920d9e0>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7e012920da80>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_7574cff83173b5ba'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "apps/frappe/frappe/database/mariadb/database.py", line 108, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "apps/frappe/frappe/database/mariadb/database.py", line 114, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "apps/frappe/frappe/database/mariadb/database.py", line 117, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e012c38d460>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      user = '_7574cff83173b5ba'
      password = ********
      host = '127.0.0.1'
      database = '_7574cff83173b5ba'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7e0129315580>, <class 'int'>: <function escape_int at 0x7e0129315620>, <class 'float'>: <function escape_float at 0x7e01293156c0>, <class 'str'>: <function escape_str at 0x7e0129315940>, <class 'bytes'>: <function escape_bytes at 0x7e01293158a0>, <class 'tuple'>: <function escape_sequence at 0x7e0129315440>, <class 'list'>: <function escape_sequence at 0x7e0129315440>, <class 'set'>: <function escape_sequence at 0x7e0129315440>, <class 'frozenset'>: <function escape_sequence at 0x7e0129315440>, <class 'dict'>: <function escape_dict at 0x7e01293153a0>, <class 'NoneType'>: <function escape_None at 0x7e01293159e0>, <class 'datetime.date'>: <function escape_date at 0x7e0129315c60>, <class 'datetime.datetime'>: <function escape_datetime at 0x7e0129315bc0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7e0129315a80>, <class 'datetime.time'>: <function escape_time at 0x7e0129315b20>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_7574cff83173b5ba\x00\x14q\xba\x025>)\xeb\xe4$\xca\x0c\xe5+D\xc2|\xf21\x93i_7574cff83173b5ba\x00mysql_native_password\x005\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x045083'
      authresp = b'q\xba\x025>)\xeb\xe4$\xca\x0c\xe5+D\xc2|\xf21\x93i'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x045083'
      k = b'_pid'
      v = b'5083'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7e01292c9df0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x19\x04#42000Unknown database \'_7574cff83173b5ba\'")
      packet_header = b'-\x00\x00\x02'
      btrl = 45
      btrh = 0
      packet_number = 2
      bytes_to_read = 45
      recv_data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      packet = <pymysql.protocol.MysqlPacket object at 0x7e01292c80d0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7e01292c80d0>
      errno = 1049
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x19\x04#42000Unknown database '_7574cff83173b5ba'"
      errno = 1049
      errval = "Unknown database '_7574cff83173b5ba'"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1049, "Unknown database '_7574cff83173b5ba'")
