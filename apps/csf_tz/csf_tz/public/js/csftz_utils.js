// Get Value - Modern async version using frappe.db.get_value
function getValue(doctype, doc_name, field_name) {
    console.warn('getValue() is deprecated. Use getValueAsync() for better performance.');
    let res = null;
    frappe.call({
        method: 'frappe.client.get_value',
        args: {
            'doctype': doctype,
            'filters': { 'name': doc_name },
            'fieldname': field_name,
        },
        async: false,
        callback: function (r) {
            if (!r.exc) {
                res = r.message[field_name];
            }
            else {
                console.error('getValue error:', r.exc);
            }
        }
    });
    return res;
}

// Modern async version of getValue
async function getValueAsync(doctype, doc_name, field_name) {
    try {
        const result = await frappe.db.get_value(doctype, { 'name': doc_name }, field_name);
        return result.message ? result.message[field_name] : null;
    } catch (error) {
        console.error('getValueAsync error:', error);
        return null;
    }
}

// Get Doc - Legacy sync version (deprecated)
function getDoc(doctype, doc_name) {
    console.warn('getDoc() is deprecated. Use getDocAsync() for better performance.');
    let res = null;
    frappe.call({
        method: 'frappe.client.get',
        args: {
            'doctype': doctype,
            'name': doc_name,
        },
        async: false,
        callback: function (r) {
            if (!r.exc) {
                res = r.message;
            }
            else {
                console.error('getDoc error:', r.exc);
            }
        }
    });
    return res;
}

// Modern async version of getDoc
async function getDocAsync(doctype, doc_name) {
    try {
        const result = await frappe.db.get_doc(doctype, doc_name);
        return result;
    } catch (error) {
        console.error('getDocAsync error:', error);
        return null;
    }
}

// Get List - Legacy sync version (deprecated)
function getList(doctype, filters, fields, order_by, limit_page_length, limit_start) {
    console.warn('getList() is deprecated. Use getListAsync() for better performance.');
    let res = null;
    frappe.call({
        method: 'frappe.client.get_list',
        args: {
            'doctype': doctype,
            'filters': filters,
            'fields': fields,
            'order_by': order_by,
            'limit_page_length': limit_page_length,
            'limit_start': limit_start,
        },
        async: false,
        callback: function (r) {
            if (!r.exc) {
                res = r.message;
            }
            else {
                console.error('getList error:', r.exc);
            }
        }
    });
    return res;
}

// Modern async version of getList
async function getListAsync(doctype, options = {}) {
    try {
        const args = {
            doctype: doctype,
            filters: options.filters || {},
            fields: options.fields || ['name'],
            order_by: options.order_by,
            limit: options.limit_page_length || options.limit || 20,
            start: options.limit_start || options.start || 0
        };

        const result = await frappe.db.get_list(doctype, args);
        return result;
    } catch (error) {
        console.error('getListAsync error:', error);
        return [];
    }
}

// Utility function for getting single values from Single DocTypes
async function getSingleValueAsync(doctype, field_name) {
    try {
        const result = await frappe.db.get_single_value(doctype, field_name);
        return result;
    } catch (error) {
        console.error('getSingleValueAsync error:', error);
        return null;
    }
}
